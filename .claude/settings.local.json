{"permissions": {"allow": ["Bash(go run cmd/test_errors/main.go)", "<PERSON><PERSON>(find . -name \"*.proto\" -path \"*/errcodes/*\")", "<PERSON><PERSON>(make errors)", "Bash(/usr/bin/make:*)", "Bash(go build ./...)", "Bash(find:*)", "Bash(go generate ./...)", "Bash(go generate ./internal/data/schema)", "<PERSON><PERSON>(go test:*)", "Bash(go build:*)", "Bash(grep:*)", "mcp__ide__getDiagnostics", "Bash(find /Users/<USER>/code/kratos-layout -name \"validate.go\" -type f)", "Bash(find /Users/<USER>/code/kratos-layout -name \"*validate*\" -type f)"], "deny": []}}