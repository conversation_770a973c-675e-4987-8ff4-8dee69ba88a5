# 项目结构指导文档

## 脚手架项目性质

此项目作为**Kratos微服务脚手架和架构参考**。在修改结构时，确保它保持作为开发团队可用作新项目起点的清晰、示范模板。

## 目录组织

### 根级结构

```
kratos-layout/
├── api/                    # 公共API定义（proto文件）
├── cmd/server/            # 应用入口点
├── configs/               # 配置文件
├── internal/              # 内部应用代码（不被其他项目导入）
├── protos/errcodes/       # 错误码proto定义
├── third_party/           # 第三方proto依赖
└── deploy/                # 部署配置
```

### Internal包结构

```
internal/
├── biz/                   # 业务逻辑层（用例）
├── data/                  # 数据层（仓储、数据库）
├── domain/                # 领域模型和接口
├── gen/                   # 生成的代码（proto、ent、错误）
├── server/                # 服务器设置（HTTP、gRPC）
├── service/               # 服务层（API处理器）
└── utils/                 # 工具函数
```

## 文件命名模式

### Go文件

- 使用下划线命名：`user_service.go`、`error_handler.go`
- 文件名与主要类型匹配：`user.go`对应`User`结构体
- 测试文件：`user_test.go`、`service_test.go`

### Proto文件

- 使用下划线命名：`user.proto`、`common_errors.proto`
- 版本在目录路径中：`api/user/v1/user.proto`
- 错误码在专用目录：`protos/errcodes/user_errors.proto`

### 生成代码位置

- API生成代码：`internal/gen/api/user/v1/`
- 错误码：`internal/gen/errcodes/`
- 数据库模式：`internal/gen/database/ent/`

## 组件架构

### 分层架构模式

遵循严格的层级依赖（从上到下）：

1. **Service层**（`internal/service/`）- API处理器、DTO转换
2. **Biz层**（`internal/biz/`）- 业务逻辑、用例
3. **Domain层**（`internal/domain/`）- 领域模型、业务规则
4. **Data层**（`internal/data/`）- 仓储、数据库访问

### 接口定义

- 在domain层定义接口：`domain.UserRepo`、`domain.UserUsecase`
- 在相应层实现接口：Biz实现UseCase，Data实现Repo
- 通过Wire使用依赖注入连接各层

### 校验流程

遵循此模式作为所有脚手架项目的标准方法：

- **Proto校验**: 由中间件处理，在.proto文件中定义
- **Service校验**: 服务层中的DTO转换、上下文提取
- **业务校验**: Biz层中调用领域校验的业务规则
- **Domain校验**: 领域对象中的复杂规则（`user.Validate()`）

## 模板维护指导

### 结构一致性

- 保持清晰的层级分离作为团队参考
- 保持目录结构简单直观，便于新项目设置
- 确保所有层都有完整的工作示例
- 记录团队应遵循的任何结构决策

### 脚手架最佳实践

- 在所有目录和文件中使用一致的命名模式
- 提供完整的实现而不是存根代码
- 在每一层包含正确的错误处理示例
- 保持生成代码与手写代码的清晰分离

## 关键文件位置

### 入口点

- `cmd/server/main.go` - 应用入口点
- `cmd/server/wire.go` - 依赖注入设置

### 配置

- `configs/config.yaml` - 主配置文件
- `internal/conf/conf.proto` - 配置结构定义

### API定义

- `api/user/v1/user.proto` - 用户服务API定义
- `protos/errcodes/*.proto` - 错误码定义

### 核心业务逻辑

- `internal/domain/user.go` - 用户领域模型和业务规则
- `internal/biz/user.go` - 用户业务逻辑用例
- `internal/service/user.go` - 用户服务API处理器

### 数据层

- `internal/data/user_repo.go` - 用户仓储实现
- `internal/data/schema/user.go` - 数据库模式定义

### 生成代码（请勿编辑）

- `internal/gen/api/` - 从proto生成的API代码
- `internal/gen/errcodes/` - 生成的错误码
- `internal/gen/database/ent/` - 生成的数据库ORM代码