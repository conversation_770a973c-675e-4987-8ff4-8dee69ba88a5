# 产品指导文档

## 项目定位

这是一个**基于Kratos框架的项目脚手架和参考模板**，旨在作为新微服务项目的起点。它展示了分层校验架构，具有清晰的职责分离和高效的参数校验模式，供开发团队遵循。

## 核心特性

- **分层校验架构**: 在Proto、Service、Biz、Domain和Data层之间清晰分离校验职责
- **性能优化校验**: 避免重复校验，提升性能和可维护性  
- **完整业务示例**: 完整的用户管理业务流程演示
- **最佳实践实现**: DDD和Clean Architecture模式的实际应用

## 用户价值

为开发团队提供一个**生产就绪的脚手架**和**架构参考**，用于构建可扩展的微服务。团队可以克隆此项目作为起点，并遵循其模式作为编码标准和最佳实践：

- 跨应用层的正确校验策略
- 清晰的架构边界和职责分离  
- 遵循DDD原则的可维护代码结构
- 一致的错误处理和业务规则实现

## 关键业务逻辑规则

### 用户管理规则

- 18岁以下用户不能被分配管理员角色
- 邮箱地址在系统中必须唯一
- 包含"admin"的用户名需要管理员或超级管理员角色
- 用户不能删除自己
- 管理员用户只能修改其租户内的用户
- 超级管理员可以修改除自己以外的任何用户

### 校验职责矩阵

- **Proto层**: 格式、类型、基础范围校验（性能影响最低）
- **Service层**: DTO转换、上下文提取（性能影响较低）  
- **Biz层**: 业务流程、权限、存在性检查（性能影响中等）
- **Domain层**: 业务规则、状态转换、跨字段校验（性能影响较低）

### 错误处理

使用带有元数据的结构化错误码，确保所有层的错误响应一致。优先使用生成的特定错误类型而不是通用错误。