# 技术指导文档

## 项目性质

这是一个**Kratos微服务脚手架项目**，既作为起始模板也作为编码标准参考。在开发此项目时，请保持其作为示范实现的作用，供其他团队复制和遵循。

## 技术栈

- **框架**: Go-Kratos v2.8.4 微服务框架
- **语言**: Go 1.24
- **数据库**: PostgreSQL配合Ent ORM (v0.14.3)
- **校验**: proto校验使用buf.validate，自定义domain校验
- **依赖注入**: Google Wire进行依赖注入
- **日志**: Zerolog结构化日志
- **API**: gRPC + HTTP网关，带OpenAPI文档
- **测试**: Go标准测试配合testify断言

## 构建系统和依赖

### 核心依赖

- Kratos框架和中间件，用于HTTP/gRPC服务器
- Ent用于数据库模式和代码生成
- Protocol Buffers配合buf.validate进行API定义
- Wire用于依赖注入代码生成

### 常用命令

```bash
# 环境初始化
make init              # 安装必需工具和依赖

# 代码生成
make api              # 从proto文件生成API代码（包含错误码）
make config           # 生成内部proto配置代码
make ent              # 生成Ent数据库模式代码
make wire             # 生成Wire依赖注入代码
make all              # 生成所有代码（api + config + generate）
make generate         # 运行go generate并整理模块

# 构建和运行
make build            # 构建二进制文件到./bin/
./bin/server -conf configs/config.yaml  # 运行服务器

# 测试
go test ./...         # 运行所有测试
go test ./internal/domain     # 运行特定包测试
go test ./internal/service    # 运行服务层测试
```

## 项目特定约定

### 脚手架维护原则

- **示范代码质量**: 所有代码都应展示团队可遵循的最佳实践
- **完整示例**: 提供完整的实现示例而非部分演示
- **清晰文档**: 每个模式都应有详细文档供学习
- **一致标准**: 在整个项目中保持一致的编码风格和架构模式

### 代码生成工作流

proto变更后始终运行代码生成：

1. 更新`api/`或`protos/errcodes/`中的proto文件
2. 运行`make api`重新生成API和错误码
3. 运行`make generate`进行额外代码生成
4. 如有依赖注入变更，运行`make wire`

### 错误处理

- 使用`internal/gen/errcodes/`中生成的错误码
- 优先使用带元数据的结构化错误而不是通用错误
- 遵循模式：`ec.ErrorTypeName.WithMetadata(map[string]string{...})`

### 校验策略

- Proto校验：在.proto文件中使用buf.validate约束
- Service校验：专注于DTO转换和上下文提取
- 业务校验：在Biz层实现业务规则
- Domain校验：复杂的跨字段和业务逻辑校验

### 导入约定

- 使用生成的API导入：`userv1 "github.com/go-kratos/kratos-layout/internal/gen/api/user/v1"`
- 使用错误码导入：`ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"`
- 遵循领域驱动的导入组织