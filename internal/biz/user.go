package biz

import (
	"context"
	"time"

	"github.com/go-kratos/kratos-layout/internal/domain"
	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
	"github.com/go-kratos/kratos/v2/log"
)

// UserUsecase 用户用例
type UserUsecase struct {
	repo domain.UserRepo
	log  *log.Helper
}

var _ domain.UserUsecase = (*UserUsecase)(nil)

// NewUserUsecase 创建用户用例实例
func NewUserUsecase(repo domain.UserRepo, logger log.Logger) domain.UserUsecase {
	return &UserUsecase{
		repo: repo,
		log:  log.NewHelper(logger),
	}
}

func (uc *UserUsecase) Create(ctx context.Context, u *domain.User) (*domain.User, error) {
	// 1. 领域对象校验（复杂业务规则）
	if err := u.Validate(); err != nil {
		return nil, err
	}

	// 2. 业务流程校验：邮箱唯一性检查
	if u.Email != "" {
		exists, err := uc.repo.ExistsByEmail(ctx, u.Email)
		if err != nil {
			return nil, err
		}
		if exists {
			return nil, ec.ErrorEmailConflict.WithMetadata(map[string]string{
				"email": u.Email,
			})
		}
	}

	// 5. 设置创建时间
	now := time.Now()
	u.CreatedAt = now
	u.UpdatedAt = now

	// 6. 执行创建
	createdUser, err := uc.repo.Create(ctx, u)
	if err != nil {
		return nil, err
	}

	uc.log.Infow("用户创建成功", "user_id", createdUser.ID, "name", createdUser.Name)
	return createdUser, nil
}

func (uc *UserUsecase) Update(ctx context.Context, u *domain.User) error {
	if err := u.Validate(); err != nil {
		return err
	}

	return uc.repo.Update(ctx, u)
}

func (uc *UserUsecase) Delete(ctx context.Context, id uint64) error {
	return uc.repo.Delete(ctx, id)
}

func (uc *UserUsecase) Get(ctx context.Context, id uint64) (*domain.User, error) {
	return uc.repo.Get(ctx, id)
}

func (uc *UserUsecase) List(ctx context.Context, pageNum, pageSize int32) ([]*domain.User, int, error) {
	return uc.repo.List(ctx, pageNum, pageSize)
}
