package service

import (
	"context"

	"github.com/go-kratos/kratos-layout/internal/domain"
	userv1 "github.com/go-kratos/kratos-layout/internal/gen/api/user/v1"

	"github.com/go-kratos/kratos/v2/log"
	"google.golang.org/protobuf/types/known/emptypb"
)

type UserService struct {
	userv1.UnimplementedUserServer

	uc  domain.UserUsecase
	log *log.Helper
}

func NewUserService(uc domain.UserUsecase, logger log.Logger) *UserService {
	return &UserService{
		uc:  uc,
		log: log.NewHelper(logger),
	}
}

func (s *UserService) CreateUser(ctx context.Context, req *userv1.CreateUserRequest) (*userv1.CreateUserReply, error) {
	// ✅ 专注于DTO转换和上下文处理，proto校验已在中间件层完成
	domainUser := &domain.User{
		Name:     req.Name,
		Age:      int(req.Age),
		TenantID: req.TenantId,
		Email:    req.GetEmail(),
		Role:     convertProtoRole(req.Role),
		// 从上下文中提取操作者信息
		CreatorId: s.extractUserID(ctx),
	}

	// 调用业务层创建用户（业务校验在Biz层进行）
	createdUser, err := s.uc.Create(ctx, domainUser)
	if err != nil {
		return nil, err
	}

	s.log.Infow("用户创建成功", "user_id", createdUser.ID, "name", createdUser.Name)
	return &userv1.CreateUserReply{
		Id: createdUser.ID,
	}, nil
}

func (s *UserService) UpdateUser(ctx context.Context, req *userv1.UpdateUserRequest) (*userv1.UpdateUserReply, error) {
	// proto 校验已在中间件层完成，这里只处理业务逻辑
	// 可以添加跨 RPC 的校验，如认证信息、权限检查等

	// 构建更新的用户对象，只包含提供的字段
	updateUser := &domain.User{
		ID:    req.Id,
		Name:  req.GetName(),
		Age:   int(req.GetAge()),
		Email: req.GetEmail(),
	}

	err := s.uc.Update(ctx, updateUser)
	if err != nil {
		return nil, err
	}

	return &userv1.UpdateUserReply{
		Success: true,
	}, nil
}

func (s *UserService) DeleteUser(ctx context.Context, req *userv1.DeleteUserRequest) (*emptypb.Empty, error) {
	// proto 校验已在中间件层完成，这里可以添加权限检查等业务逻辑

	err := s.uc.Delete(ctx, req.Id)
	if err != nil {
		return nil, err
	}
	return &emptypb.Empty{}, nil
}

func (s *UserService) ListUsers(ctx context.Context, req *userv1.ListUsersRequest) (*userv1.ListUsersReply, error) {
	// proto 校验已在中间件层完成，这里可以添加权限检查等业务逻辑

	users, total, err := s.uc.List(ctx, int32(req.PageNum), int32(req.PageSize))
	if err != nil {
		return nil, err
	}

	reply := &userv1.ListUsersReply{
		Total: uint32(total),
		Users: make([]*userv1.UserInfo, 0, len(users)),
	}

	for _, u := range users {
		reply.Users = append(reply.Users, &userv1.UserInfo{
			Id:        u.ID,
			Name:      u.Name,
			Age:       int32(u.Age),
			TenantId:  u.TenantID,
			Email:     u.Email,
			Role:      convertDomainRole(u.Role),
			CreatedAt: u.CreatedAt.Unix(),
			UpdatedAt: u.UpdatedAt.Unix(),
		})
	}

	return reply, nil
}

// 辅助方法：提取上下文中的操作者ID
func (s *UserService) extractUserID(ctx context.Context) uint64 {
	if operatorID, ok := ctx.Value("x-user-id").(uint64); ok {
		return operatorID
	}
	return 0
}

// 辅助方法：将proto角色转换为domain角色
func convertProtoRole(protoRole userv1.UserRole) domain.UserRole {
	switch protoRole {
	case userv1.UserRole_USER_ROLE_USER:
		return domain.UserRoleUser
	case userv1.UserRole_USER_ROLE_ADMIN:
		return domain.UserRoleAdmin
	case userv1.UserRole_USER_ROLE_SUPER_ADMIN:
		return domain.UserRoleSuperAdmin
	default:
		return domain.UserRoleUser // 默认为普通用户
	}
}

// 辅助方法：将domain角色转换为proto角色
func convertDomainRole(domainRole domain.UserRole) userv1.UserRole {
	switch domainRole {
	case domain.UserRoleUser:
		return userv1.UserRole_USER_ROLE_USER
	case domain.UserRoleAdmin:
		return userv1.UserRole_USER_ROLE_ADMIN
	case domain.UserRoleSuperAdmin:
		return userv1.UserRole_USER_ROLE_SUPER_ADMIN
	default:
		return userv1.UserRole_USER_ROLE_USER
	}
}

