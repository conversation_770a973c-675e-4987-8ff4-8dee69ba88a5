package domain

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestUser_Validate(t *testing.T) {
	tests := []struct {
		name    string
		user    *User
		wantErr bool
		errMsg  string
	}{
		{
			name: "有效的普通用户",
			user: &User{
				Name:     "张三",
				Age:      25,
				TenantID: 1001,
				Role:     UserRoleUser,
			},
			wantErr: false,
		},
		{
			name: "有效的成年管理员",
			user: &User{
				Name:     "李四",
				Age:      25,
				TenantID: 1001,
				Role:     UserRoleAdmin,
			},
			wantErr: false,
		},
		{
			name: "未成年管理员 - 应该失败",
			user: &User{
				Name:     "小明",
				Age:      16,
				TenantID: 1001,
				Role:     UserRoleAdmin,
			},
			wantErr: true,
			errMsg:  "年龄不符合角色要求",
		},
		{
			name: "有效的超级管理员 - 应该成功",
			user: &User{
				Name:     "超级管理员",
				Age:      30,
				TenantID: 0,
				Role:     UserRoleSuperAdmin,
			},
			wantErr: false,
		},
		{
			name: "普通用户名包含admin关键词 - 应该失败",
			user: &User{
				Name:     "admin_user",
				Age:      25,
				TenantID: 1001,
				Role:     UserRoleUser,
			},
			wantErr: true,
			errMsg:  "用户名包含敏感词汇",
		},
		{
			name: "管理员用户名包含admin关键词 - 应该成功",
			user: &User{
				Name:     "admin_manager",
				Age:      25,
				TenantID: 1001,
				Role:     UserRoleAdmin,
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.user.Validate()
			if tt.wantErr {
				assert.Error(t, err)
				if tt.errMsg != "" {
					assert.Contains(t, err.Error(), tt.errMsg)
				}
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestUser_CanModify(t *testing.T) {
	user := &User{
		ID:       1,
		Name:     "张三",
		Age:      25,
		TenantID: 1001,
		Role:     UserRoleUser,
	}

	tests := []struct {
		name             string
		operatorID       uint64
		operatorRole     UserRole
		operatorTenantID uint64
		expected         bool
	}{
		{
			name:             "用户修改自己",
			operatorID:       1,
			operatorRole:     UserRoleUser,
			operatorTenantID: 1001,
			expected:         true,
		},
		{
			name:             "超级管理员修改任何用户",
			operatorID:       2,
			operatorRole:     UserRoleSuperAdmin,
			operatorTenantID: 0,
			expected:         true,
		},
		{
			name:             "同租户管理员修改普通用户",
			operatorID:       3,
			operatorRole:     UserRoleAdmin,
			operatorTenantID: 1001,
			expected:         true,
		},
		{
			name:             "不同租户管理员不能修改",
			operatorID:       4,
			operatorRole:     UserRoleAdmin,
			operatorTenantID: 1002,
			expected:         false,
		},
		{
			name:             "普通用户不能修改其他用户",
			operatorID:       5,
			operatorRole:     UserRoleUser,
			operatorTenantID: 1001,
			expected:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := user.CanModify(tt.operatorID, tt.operatorRole, tt.operatorTenantID)
			assert.Equal(t, tt.expected, result)
		})
	}
}

func TestUser_CanDelete(t *testing.T) {
	user := &User{
		ID:       1,
		Name:     "张三",
		Age:      25,
		TenantID: 1001,
		Role:     UserRoleUser,
	}

	tests := []struct {
		name             string
		operatorID       uint64
		operatorRole     UserRole
		operatorTenantID uint64
		expected         bool
	}{
		{
			name:             "用户不能删除自己",
			operatorID:       1,
			operatorRole:     UserRoleUser,
			operatorTenantID: 1001,
			expected:         false,
		},
		{
			name:             "超级管理员可以删除其他用户",
			operatorID:       2,
			operatorRole:     UserRoleSuperAdmin,
			operatorTenantID: 0,
			expected:         true,
		},
		{
			name:             "同租户管理员可以删除普通用户",
			operatorID:       3,
			operatorRole:     UserRoleAdmin,
			operatorTenantID: 1001,
			expected:         true,
		},
		{
			name:             "不同租户管理员不能删除",
			operatorID:       4,
			operatorRole:     UserRoleAdmin,
			operatorTenantID: 1002,
			expected:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := user.CanDelete(tt.operatorID, tt.operatorRole, tt.operatorTenantID)
			assert.Equal(t, tt.expected, result)
		})
	}
}


func TestUser_HasRole(t *testing.T) {
	user := &User{Role: UserRoleAdmin}
	
	assert.True(t, user.HasRole(UserRoleAdmin))
	assert.False(t, user.HasRole(UserRoleUser))
	assert.False(t, user.HasRole(UserRoleSuperAdmin))
}

func TestUser_IsAdminOrAbove(t *testing.T) {
	tests := []struct {
		name     string
		role     UserRole
		expected bool
	}{
		{"普通用户", UserRoleUser, false},
		{"管理员", UserRoleAdmin, true},
		{"超级管理员", UserRoleSuperAdmin, true},
		{"未指定", UserRoleUnspecified, false},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			user := &User{Role: tt.role}
			result := user.IsAdminOrAbove()
			assert.Equal(t, tt.expected, result)
		})
	}
}

