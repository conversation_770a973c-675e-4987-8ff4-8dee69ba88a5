package domain

import (
	"context"
	"fmt"
	"strings"
	"time"

	ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
)

// UserRole 用户角色枚举
type UserRole int32

const (
	UserRoleUnspecified UserRole = 0
	UserRoleUser        UserRole = 1 // 普通用户
	UserRoleAdmin       UserRole = 2 // 管理员
	UserRoleSuperAdmin  UserRole = 3 // 超级管理员
)

func (r UserRole) String() string {
	switch r {
	case UserRoleUser:
		return "user"
	case UserRoleAdmin:
		return "admin"
	case UserRoleSuperAdmin:
		return "super_admin"
	default:
		return "unspecified"
	}
}

// User 领域模型
type User struct {
	ID        uint64
	Name      string
	Age       int
	TenantID  uint64
	Email     string
	Role      UserRole
	CreatorId uint64
	CreatedAt time.Time
	UpdatedAt time.Time
}

// UserRepository 用户仓储接口
type UserRepo interface {
	Create(context.Context, *User) (*User, error)
	Update(context.Context, *User) error
	Delete(context.Context, uint64) error
	Get(context.Context, uint64) (*User, error)
	List(context.Context, int32, int32) ([]*User, int, error)
	ExistsByEmail(ctx context.Context, email string) (bool, error)
}

// UserUsecase 定义用例接口
type UserUsecase interface {
	Create(ctx context.Context, u *User) (*User, error)
	Update(ctx context.Context, u *User) error
	Delete(ctx context.Context, id uint64) error
	Get(ctx context.Context, id uint64) (*User, error)
	List(ctx context.Context, pageNum, pageSize int32) ([]*User, int, error)
}

// Validate 域对象的业务规则校验（不同于 proto 格式校验）
func (u *User) Validate() error {
	// 基本的格式校验已在 proto 层完成，这里主要做业务规则校验

	// 1. 跨字段关联校验：未成年人不能担任管理员
	if u.Age < 18 && u.Role == UserRoleAdmin {
		return ec.ErrorAgeRoleMismatch.WithMetadata(map[string]string{
			"age":    fmt.Sprintf("%d", u.Age),
			"role":   u.Role.String(),
			"reason": "管理员角色需要年满18岁",
			"min_age": "18",
		})
	}


	// 2. 敏感词汇检查（示例）
	if strings.Contains(strings.ToLower(u.Name), "admin") && u.Role != UserRoleAdmin && u.Role != UserRoleSuperAdmin {
		return ec.ErrorUsernameSensitiveWords.WithMetadata(map[string]string{
			"name":       u.Name,
			"role":       u.Role.String(),
			"reason":     "用户名包含敏感词汇",
			"keyword":    "admin",
			"allowed_roles": "admin,super_admin",
		})
	}

	return nil
}

// CanModify 检查用户是否可以被指定操作者修改
func (u *User) CanModify(operatorID uint64, operatorRole UserRole, operatorTenantID uint64) bool {
	// 用户可以修改自己
	if u.ID == operatorID {
		return true
	}

	// 超级管理员可以修改任何用户
	if operatorRole == UserRoleSuperAdmin {
		return true
	}

	// 管理员可以修改同租户的普通用户
	if operatorRole == UserRoleAdmin && operatorTenantID == u.TenantID && u.Role == UserRoleUser {
		return true
	}

	return false
}

// CanDelete 检查用户是否可以被删除
func (u *User) CanDelete(operatorID uint64, operatorRole UserRole, operatorTenantID uint64) bool {
	// 用户不能删除自己
	if u.ID == operatorID {
		return false
	}

	// 超级管理员可以删除除自己外的任何用户
	if operatorRole == UserRoleSuperAdmin {
		return true
	}

	// 管理员可以删除同租户的普通用户
	if operatorRole == UserRoleAdmin && operatorTenantID == u.TenantID && u.Role == UserRoleUser {
		return true
	}

	return false
}

// HasRole 检查用户是否具有指定角色
func (u *User) HasRole(role UserRole) bool {
	return u.Role == role
}

// IsAdminOrAbove 检查用户是否是管理员或更高级别
func (u *User) IsAdminOrAbove() bool {
	return u.Role == UserRoleAdmin || u.Role == UserRoleSuperAdmin
}
