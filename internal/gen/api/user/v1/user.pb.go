// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: user/v1/user.proto

package v1

import (
	_ "buf.build/gen/go/bufbuild/protovalidate/protocolbuffers/go/buf/validate"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 用户角色枚举
type UserRole int32

const (
	UserRole_USER_ROLE_UNSPECIFIED UserRole = 0
	UserRole_USER_ROLE_USER        UserRole = 1 // 普通用户
	UserRole_USER_ROLE_ADMIN       UserRole = 2 // 管理员
	UserRole_USER_ROLE_SUPER_ADMIN UserRole = 3 // 超级管理员
)

// Enum value maps for UserRole.
var (
	UserRole_name = map[int32]string{
		0: "USER_ROLE_UNSPECIFIED",
		1: "USER_ROLE_USER",
		2: "USER_ROLE_ADMIN",
		3: "USER_ROLE_SUPER_ADMIN",
	}
	UserRole_value = map[string]int32{
		"USER_ROLE_UNSPECIFIED": 0,
		"USER_ROLE_USER":        1,
		"USER_ROLE_ADMIN":       2,
		"USER_ROLE_SUPER_ADMIN": 3,
	}
)

func (x UserRole) Enum() *UserRole {
	p := new(UserRole)
	*p = x
	return p
}

func (x UserRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserRole) Descriptor() protoreflect.EnumDescriptor {
	return file_user_v1_user_proto_enumTypes[0].Descriptor()
}

func (UserRole) Type() protoreflect.EnumType {
	return &file_user_v1_user_proto_enumTypes[0]
}

func (x UserRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserRole.Descriptor instead.
func (UserRole) EnumDescriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

// 创建用户请求
type CreateUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户名：支持中文、英文字母
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// 年龄：1-150岁
	Age int32 `protobuf:"varint,2,opt,name=age,proto3" json:"age,omitempty"`
	// 租户ID：必须大于0
	TenantId uint64 `protobuf:"varint,3,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// 邮箱：可选字段，如果提供则必须符合邮箱格式
	Email *string `protobuf:"bytes,4,opt,name=email,proto3,oneof" json:"email,omitempty"`
	// 用户角色：默认为普通用户
	Role          UserRole `protobuf:"varint,6,opt,name=role,proto3,enum=user.v1.UserRole" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserRequest) Reset() {
	*x = CreateUserRequest{}
	mi := &file_user_v1_user_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserRequest) ProtoMessage() {}

func (x *CreateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserRequest.ProtoReflect.Descriptor instead.
func (*CreateUserRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{0}
}

func (x *CreateUserRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateUserRequest) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *CreateUserRequest) GetTenantId() uint64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *CreateUserRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

func (x *CreateUserRequest) GetRole() UserRole {
	if x != nil {
		return x.Role
	}
	return UserRole_USER_ROLE_UNSPECIFIED
}

// 创建用户响应
type CreateUserReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserReply) Reset() {
	*x = CreateUserReply{}
	mi := &file_user_v1_user_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserReply) ProtoMessage() {}

func (x *CreateUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserReply.ProtoReflect.Descriptor instead.
func (*CreateUserReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUserReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 更新用户请求
type UpdateUserRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// 用户ID：必须大于0
	Id uint64 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	// 用户名：支持中文、英文字母
	Name *string `protobuf:"bytes,2,opt,name=name,proto3,oneof" json:"name,omitempty"`
	// 年龄：1-150岁
	Age *int32 `protobuf:"varint,3,opt,name=age,proto3,oneof" json:"age,omitempty"`
	// 邮箱：可选字段
	Email         *string `protobuf:"bytes,4,opt,name=email,proto3,oneof" json:"email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserRequest) Reset() {
	*x = UpdateUserRequest{}
	mi := &file_user_v1_user_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserRequest) ProtoMessage() {}

func (x *UpdateUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateUserRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserRequest) GetName() string {
	if x != nil && x.Name != nil {
		return *x.Name
	}
	return ""
}

func (x *UpdateUserRequest) GetAge() int32 {
	if x != nil && x.Age != nil {
		return *x.Age
	}
	return 0
}

func (x *UpdateUserRequest) GetEmail() string {
	if x != nil && x.Email != nil {
		return *x.Email
	}
	return ""
}

// 更新用户响应
type UpdateUserReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateUserReply) Reset() {
	*x = UpdateUserReply{}
	mi := &file_user_v1_user_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserReply) ProtoMessage() {}

func (x *UpdateUserReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserReply.ProtoReflect.Descriptor instead.
func (*UpdateUserReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateUserReply) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

// 删除用户请求
type DeleteUserRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserRequest) Reset() {
	*x = DeleteUserRequest{}
	mi := &file_user_v1_user_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserRequest) ProtoMessage() {}

func (x *DeleteUserRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteUserRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

// 用户列表请求
type ListUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PageSize      uint32                 `protobuf:"varint,1,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	PageNum       uint32                 `protobuf:"varint,2,opt,name=page_num,json=pageNum,proto3" json:"page_num,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUsersRequest) Reset() {
	*x = ListUsersRequest{}
	mi := &file_user_v1_user_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersRequest) ProtoMessage() {}

func (x *ListUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersRequest.ProtoReflect.Descriptor instead.
func (*ListUsersRequest) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{5}
}

func (x *ListUsersRequest) GetPageSize() uint32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListUsersRequest) GetPageNum() uint32 {
	if x != nil {
		return x.PageNum
	}
	return 0
}

// 用户信息
type UserInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Age           int32                  `protobuf:"varint,3,opt,name=age,proto3" json:"age,omitempty"`
	TenantId      uint64                 `protobuf:"varint,4,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	Email         string                 `protobuf:"bytes,5,opt,name=email,proto3" json:"email,omitempty"`
	Role          UserRole               `protobuf:"varint,7,opt,name=role,proto3,enum=user.v1.UserRole" json:"role,omitempty"`
	CreatedAt     int64                  `protobuf:"varint,9,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`  // 创建时间（Unix时间戳）
	UpdatedAt     int64                  `protobuf:"varint,10,opt,name=updated_at,json=updatedAt,proto3" json:"updated_at,omitempty"` // 更新时间（Unix时间戳）
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_user_v1_user_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{6}
}

func (x *UserInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UserInfo) GetAge() int32 {
	if x != nil {
		return x.Age
	}
	return 0
}

func (x *UserInfo) GetTenantId() uint64 {
	if x != nil {
		return x.TenantId
	}
	return 0
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetRole() UserRole {
	if x != nil {
		return x.Role
	}
	return UserRole_USER_ROLE_UNSPECIFIED
}

func (x *UserInfo) GetCreatedAt() int64 {
	if x != nil {
		return x.CreatedAt
	}
	return 0
}

func (x *UserInfo) GetUpdatedAt() int64 {
	if x != nil {
		return x.UpdatedAt
	}
	return 0
}

// 用户列表响应
type ListUsersReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Users         []*UserInfo            `protobuf:"bytes,1,rep,name=users,proto3" json:"users,omitempty"`
	Total         uint32                 `protobuf:"varint,2,opt,name=total,proto3" json:"total,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListUsersReply) Reset() {
	*x = ListUsersReply{}
	mi := &file_user_v1_user_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListUsersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListUsersReply) ProtoMessage() {}

func (x *ListUsersReply) ProtoReflect() protoreflect.Message {
	mi := &file_user_v1_user_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListUsersReply.ProtoReflect.Descriptor instead.
func (*ListUsersReply) Descriptor() ([]byte, []int) {
	return file_user_v1_user_proto_rawDescGZIP(), []int{7}
}

func (x *ListUsersReply) GetUsers() []*UserInfo {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *ListUsersReply) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

var File_user_v1_user_proto protoreflect.FileDescriptor

const file_user_v1_user_proto_rawDesc = "" +
	"\n" +
	"\x12user/v1/user.proto\x12\auser.v1\x1a\x1cgoogle/api/annotations.proto\x1a\x1bgoogle/protobuf/empty.proto\x1a\x1bbuf/validate/validate.proto\"\xaa\x02\n" +
	"\x11CreateUserRequest\x129\n" +
	"\x04name\x18\x01 \x01(\tB%\xbaH\"r \x10\x01\x1822\x1a^[\\u4e00-\\u9fa5a-zA-Z\\s]+$R\x04name\x12\x1c\n" +
	"\x03age\x18\x02 \x01(\x05B\n" +
	"\xbaH\a\x1a\x05\x18\x96\x01(\x01R\x03age\x12(\n" +
	"\ttenant_id\x18\x03 \x01(\x04B\v\xbaH\b2\x06\x18\xbf\x84= \x00R\btenantId\x12U\n" +
	"\x05email\x18\x04 \x01(\tB:\xbaH7\xd8\x01\x01r220^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$H\x00R\x05email\x88\x01\x01\x121\n" +
	"\x04role\x18\x06 \x01(\x0e2\x11.user.v1.UserRoleB\n" +
	"\xbaH\a\x82\x01\x04\x10\x01 \x00R\x04roleB\b\n" +
	"\x06_email\"!\n" +
	"\x0fCreateUserReply\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\"\x87\x02\n" +
	"\x11UpdateUserRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\x12A\n" +
	"\x04name\x18\x02 \x01(\tB(\xbaH%\xd8\x01\x01r \x10\x01\x1822\x1a^[\\u4e00-\\u9fa5a-zA-Z\\s]+$H\x00R\x04name\x88\x01\x01\x12$\n" +
	"\x03age\x18\x03 \x01(\x05B\r\xbaH\n" +
	"\xd8\x01\x01\x1a\x05\x18\x96\x01(\x01H\x01R\x03age\x88\x01\x01\x12U\n" +
	"\x05email\x18\x04 \x01(\tB:\xbaH7\xd8\x01\x01r220^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$H\x02R\x05email\x88\x01\x01B\a\n" +
	"\x05_nameB\x06\n" +
	"\x04_ageB\b\n" +
	"\x06_email\"+\n" +
	"\x0fUpdateUserReply\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\",\n" +
	"\x11DeleteUserRequest\x12\x17\n" +
	"\x02id\x18\x01 \x01(\x04B\a\xbaH\x042\x02 \x00R\x02id\"^\n" +
	"\x10ListUsersRequest\x12&\n" +
	"\tpage_size\x18\x01 \x01(\rB\t\xbaH\x06*\x04\x18d(\x01R\bpageSize\x12\"\n" +
	"\bpage_num\x18\x02 \x01(\rB\a\xbaH\x04*\x02 \x00R\apageNum\"\xd8\x01\n" +
	"\bUserInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\x04R\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x10\n" +
	"\x03age\x18\x03 \x01(\x05R\x03age\x12\x1b\n" +
	"\ttenant_id\x18\x04 \x01(\x04R\btenantId\x12\x14\n" +
	"\x05email\x18\x05 \x01(\tR\x05email\x12%\n" +
	"\x04role\x18\a \x01(\x0e2\x11.user.v1.UserRoleR\x04role\x12\x1d\n" +
	"\n" +
	"created_at\x18\t \x01(\x03R\tcreatedAt\x12\x1d\n" +
	"\n" +
	"updated_at\x18\n" +
	" \x01(\x03R\tupdatedAt\"O\n" +
	"\x0eListUsersReply\x12'\n" +
	"\x05users\x18\x01 \x03(\v2\x11.user.v1.UserInfoR\x05users\x12\x14\n" +
	"\x05total\x18\x02 \x01(\rR\x05total*i\n" +
	"\bUserRole\x12\x19\n" +
	"\x15USER_ROLE_UNSPECIFIED\x10\x00\x12\x12\n" +
	"\x0eUSER_ROLE_USER\x10\x01\x12\x13\n" +
	"\x0fUSER_ROLE_ADMIN\x10\x02\x12\x19\n" +
	"\x15USER_ROLE_SUPER_ADMIN\x10\x032\xed\x02\n" +
	"\x04User\x12X\n" +
	"\n" +
	"CreateUser\x12\x1a.user.v1.CreateUserRequest\x1a\x18.user.v1.CreateUserReply\"\x14\x82\xd3\xe4\x93\x02\x0e:\x01*\"\t/v1/users\x12]\n" +
	"\n" +
	"UpdateUser\x12\x1a.user.v1.UpdateUserRequest\x1a\x18.user.v1.UpdateUserReply\"\x19\x82\xd3\xe4\x93\x02\x13:\x01*\x1a\x0e/v1/users/{id}\x12X\n" +
	"\n" +
	"DeleteUser\x12\x1a.user.v1.DeleteUserRequest\x1a\x16.google.protobuf.Empty\"\x16\x82\xd3\xe4\x93\x02\x10*\x0e/v1/users/{id}\x12R\n" +
	"\tListUsers\x12\x19.user.v1.ListUsersRequest\x1a\x17.user.v1.ListUsersReply\"\x11\x82\xd3\xe4\x93\x02\v\x12\t/v1/usersB\x1eZ\x1ckratos-layout/api/user/v1;v1b\x06proto3"

var (
	file_user_v1_user_proto_rawDescOnce sync.Once
	file_user_v1_user_proto_rawDescData []byte
)

func file_user_v1_user_proto_rawDescGZIP() []byte {
	file_user_v1_user_proto_rawDescOnce.Do(func() {
		file_user_v1_user_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)))
	})
	return file_user_v1_user_proto_rawDescData
}

var file_user_v1_user_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_user_v1_user_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_user_v1_user_proto_goTypes = []any{
	(UserRole)(0),             // 0: user.v1.UserRole
	(*CreateUserRequest)(nil), // 1: user.v1.CreateUserRequest
	(*CreateUserReply)(nil),   // 2: user.v1.CreateUserReply
	(*UpdateUserRequest)(nil), // 3: user.v1.UpdateUserRequest
	(*UpdateUserReply)(nil),   // 4: user.v1.UpdateUserReply
	(*DeleteUserRequest)(nil), // 5: user.v1.DeleteUserRequest
	(*ListUsersRequest)(nil),  // 6: user.v1.ListUsersRequest
	(*UserInfo)(nil),          // 7: user.v1.UserInfo
	(*ListUsersReply)(nil),    // 8: user.v1.ListUsersReply
	(*emptypb.Empty)(nil),     // 9: google.protobuf.Empty
}
var file_user_v1_user_proto_depIdxs = []int32{
	0, // 0: user.v1.CreateUserRequest.role:type_name -> user.v1.UserRole
	0, // 1: user.v1.UserInfo.role:type_name -> user.v1.UserRole
	7, // 2: user.v1.ListUsersReply.users:type_name -> user.v1.UserInfo
	1, // 3: user.v1.User.CreateUser:input_type -> user.v1.CreateUserRequest
	3, // 4: user.v1.User.UpdateUser:input_type -> user.v1.UpdateUserRequest
	5, // 5: user.v1.User.DeleteUser:input_type -> user.v1.DeleteUserRequest
	6, // 6: user.v1.User.ListUsers:input_type -> user.v1.ListUsersRequest
	2, // 7: user.v1.User.CreateUser:output_type -> user.v1.CreateUserReply
	4, // 8: user.v1.User.UpdateUser:output_type -> user.v1.UpdateUserReply
	9, // 9: user.v1.User.DeleteUser:output_type -> google.protobuf.Empty
	8, // 10: user.v1.User.ListUsers:output_type -> user.v1.ListUsersReply
	7, // [7:11] is the sub-list for method output_type
	3, // [3:7] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_user_v1_user_proto_init() }
func file_user_v1_user_proto_init() {
	if File_user_v1_user_proto != nil {
		return
	}
	file_user_v1_user_proto_msgTypes[0].OneofWrappers = []any{}
	file_user_v1_user_proto_msgTypes[2].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_user_v1_user_proto_rawDesc), len(file_user_v1_user_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_user_v1_user_proto_goTypes,
		DependencyIndexes: file_user_v1_user_proto_depIdxs,
		EnumInfos:         file_user_v1_user_proto_enumTypes,
		MessageInfos:      file_user_v1_user_proto_msgTypes,
	}.Build()
	File_user_v1_user_proto = out.File
	file_user_v1_user_proto_goTypes = nil
	file_user_v1_user_proto_depIdxs = nil
}
