// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: errcodes/common_errors.proto

package errcodes

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// CommonErrorReason 定义通用错误码
type CommonErrorReason int32

const (
	// 内部错误
	CommonErrorReason_INTERNAL_ERROR CommonErrorReason = 0
	// 参数错误
	CommonErrorReason_BAD_REQUEST CommonErrorReason = 1
	// 认证失败
	CommonErrorReason_UNAUTHORIZED CommonErrorReason = 2
	// 权限不足
	CommonErrorReason_FORBIDDEN CommonErrorReason = 3
	// 资源不存在
	CommonErrorReason_NOT_FOUND CommonErrorReason = 4
	// 资源冲突
	CommonErrorReason_CONFLICT CommonErrorReason = 5
	// 请求太多
	CommonErrorReason_TOO_MANY_REQUEST CommonErrorReason = 6
	// 请求超时
	CommonErrorReason_REQUEST_TIMEOUT CommonErrorReason = 7
	// 服务不可用
	CommonErrorReason_SERVICE_UNAVAILABLE CommonErrorReason = 8
)

// Enum value maps for CommonErrorReason.
var (
	CommonErrorReason_name = map[int32]string{
		0: "INTERNAL_ERROR",
		1: "BAD_REQUEST",
		2: "UNAUTHORIZED",
		3: "FORBIDDEN",
		4: "NOT_FOUND",
		5: "CONFLICT",
		6: "TOO_MANY_REQUEST",
		7: "REQUEST_TIMEOUT",
		8: "SERVICE_UNAVAILABLE",
	}
	CommonErrorReason_value = map[string]int32{
		"INTERNAL_ERROR":      0,
		"BAD_REQUEST":         1,
		"UNAUTHORIZED":        2,
		"FORBIDDEN":           3,
		"NOT_FOUND":           4,
		"CONFLICT":            5,
		"TOO_MANY_REQUEST":    6,
		"REQUEST_TIMEOUT":     7,
		"SERVICE_UNAVAILABLE": 8,
	}
)

func (x CommonErrorReason) Enum() *CommonErrorReason {
	p := new(CommonErrorReason)
	*p = x
	return p
}

func (x CommonErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CommonErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_errcodes_common_errors_proto_enumTypes[0].Descriptor()
}

func (CommonErrorReason) Type() protoreflect.EnumType {
	return &file_errcodes_common_errors_proto_enumTypes[0]
}

func (x CommonErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CommonErrorReason.Descriptor instead.
func (CommonErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_errcodes_common_errors_proto_rawDescGZIP(), []int{0}
}

var File_errcodes_common_errors_proto protoreflect.FileDescriptor

const file_errcodes_common_errors_proto_rawDesc = "" +
	"\n" +
	"\x1cerrcodes/common_errors.proto\x12\berrcodes\x1a\x13errors/errors.proto*\xf6\x01\n" +
	"\x11CommonErrorReason\x12\x18\n" +
	"\x0eINTERNAL_ERROR\x10\x00\x1a\x04\xa8E\xf4\x03\x12\x15\n" +
	"\vBAD_REQUEST\x10\x01\x1a\x04\xa8E\x90\x03\x12\x16\n" +
	"\fUNAUTHORIZED\x10\x02\x1a\x04\xa8E\x91\x03\x12\x13\n" +
	"\tFORBIDDEN\x10\x03\x1a\x04\xa8E\x93\x03\x12\x13\n" +
	"\tNOT_FOUND\x10\x04\x1a\x04\xa8E\x94\x03\x12\x12\n" +
	"\bCONFLICT\x10\x05\x1a\x04\xa8E\x99\x03\x12\x1a\n" +
	"\x10TOO_MANY_REQUEST\x10\x06\x1a\x04\xa8E\xad\x03\x12\x19\n" +
	"\x0fREQUEST_TIMEOUT\x10\a\x1a\x04\xa8E\x98\x03\x12\x1d\n" +
	"\x13SERVICE_UNAVAILABLE\x10\b\x1a\x04\xa8E\xf7\x03\x1a\x04\xa0E\xf4\x03BCZAgithub.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodesb\x06proto3"

var (
	file_errcodes_common_errors_proto_rawDescOnce sync.Once
	file_errcodes_common_errors_proto_rawDescData []byte
)

func file_errcodes_common_errors_proto_rawDescGZIP() []byte {
	file_errcodes_common_errors_proto_rawDescOnce.Do(func() {
		file_errcodes_common_errors_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_errcodes_common_errors_proto_rawDesc), len(file_errcodes_common_errors_proto_rawDesc)))
	})
	return file_errcodes_common_errors_proto_rawDescData
}

var file_errcodes_common_errors_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_errcodes_common_errors_proto_goTypes = []any{
	(CommonErrorReason)(0), // 0: errcodes.CommonErrorReason
}
var file_errcodes_common_errors_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_errcodes_common_errors_proto_init() }
func file_errcodes_common_errors_proto_init() {
	if File_errcodes_common_errors_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_errcodes_common_errors_proto_rawDesc), len(file_errcodes_common_errors_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_errcodes_common_errors_proto_goTypes,
		DependencyIndexes: file_errcodes_common_errors_proto_depIdxs,
		EnumInfos:         file_errcodes_common_errors_proto_enumTypes,
	}.Build()
	File_errcodes_common_errors_proto = out.File
	file_errcodes_common_errors_proto_goTypes = nil
	file_errcodes_common_errors_proto_depIdxs = nil
}
