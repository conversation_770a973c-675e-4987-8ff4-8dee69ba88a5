// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package errcodes

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 用户未找到
func IsUserNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_NOT_FOUND.String() && e.Code == 404
}

// 用户未找到
func ErrorUserNotFoundf(format string, args ...interface{}) *errors.Error {
	return errors.New(404, UserErrorReason_USER_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 用户冲突
func IsUserConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USER_CONFLICT.String() && e.Code == 409
}

// 用户冲突
func ErrorUserConflictf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_USER_CONFLICT.String(), fmt.Sprintf(format, args...))
}

// 用户名冲突
func IsUsernameConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USERNAME_CONFLICT.String() && e.Code == 409
}

// 用户名冲突
func ErrorUsernameConflictf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_USERNAME_CONFLICT.String(), fmt.Sprintf(format, args...))
}

// 邮箱冲突
func IsEmailConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_EMAIL_CONFLICT.String() && e.Code == 409
}

// 邮箱冲突
func ErrorEmailConflictf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, UserErrorReason_EMAIL_CONFLICT.String(), fmt.Sprintf(format, args...))
}

// 年龄不符合角色要求
func IsAgeRoleMismatch(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_AGE_ROLE_MISMATCH.String() && e.Code == 400
}

// 年龄不符合角色要求
func ErrorAgeRoleMismatchf(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_AGE_ROLE_MISMATCH.String(), fmt.Sprintf(format, args...))
}

// 用户名包含敏感词汇
func IsUsernameSensitiveWords(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == UserErrorReason_USERNAME_SENSITIVE_WORDS.String() && e.Code == 400
}

// 用户名包含敏感词汇
func ErrorUsernameSensitiveWordsf(format string, args ...interface{}) *errors.Error {
	return errors.New(400, UserErrorReason_USERNAME_SENSITIVE_WORDS.String(), fmt.Sprintf(format, args...))
}

var (
	// 用户未找到
	ErrorUserNotFound *errors.Error
	// 用户冲突
	ErrorUserConflict *errors.Error
	// 用户名冲突
	ErrorUsernameConflict *errors.Error
	// 邮箱冲突
	ErrorEmailConflict *errors.Error
	// 年龄不符合角色要求
	ErrorAgeRoleMismatch *errors.Error
	// 用户名包含敏感词汇
	ErrorUsernameSensitiveWords *errors.Error
)

func init() {
	ErrorUserNotFound = ErrorUserNotFoundf("用户不存在")
	ErrorUserConflict = ErrorUserConflictf("用户已存在")
	ErrorUsernameConflict = ErrorUsernameConflictf("用户名已被使用")
	ErrorEmailConflict = ErrorEmailConflictf("邮箱已被使用")
	ErrorAgeRoleMismatch = ErrorAgeRoleMismatchf("年龄不符合角色要求")
	ErrorUsernameSensitiveWords = ErrorUsernameSensitiveWordsf("用户名包含敏感词汇")
}
