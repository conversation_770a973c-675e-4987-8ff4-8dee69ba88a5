// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: errcodes/user_errors.proto

package errcodes

import (
	_ "github.com/go-kratos/kratos/v2/errors"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// UserErrorReason 定义用户服务相关错误码
type UserErrorReason int32

const (
	// 用户未找到
	UserErrorReason_USER_NOT_FOUND UserErrorReason = 0
	// 用户冲突
	UserErrorReason_USER_CONFLICT UserErrorReason = 1
	// 用户名冲突
	UserErrorReason_USERNAME_CONFLICT UserErrorReason = 4
	// 邮箱冲突
	UserErrorReason_EMAIL_CONFLICT UserErrorReason = 7
	// 年龄不符合角色要求
	UserErrorReason_AGE_ROLE_MISMATCH UserErrorReason = 8
	// 用户名包含敏感词汇
	UserErrorReason_USERNAME_SENSITIVE_WORDS UserErrorReason = 9
)

// Enum value maps for UserErrorReason.
var (
	UserErrorReason_name = map[int32]string{
		0: "USER_NOT_FOUND",
		1: "USER_CONFLICT",
		4: "USERNAME_CONFLICT",
		7: "EMAIL_CONFLICT",
		8: "AGE_ROLE_MISMATCH",
		9: "USERNAME_SENSITIVE_WORDS",
	}
	UserErrorReason_value = map[string]int32{
		"USER_NOT_FOUND":           0,
		"USER_CONFLICT":            1,
		"USERNAME_CONFLICT":        4,
		"EMAIL_CONFLICT":           7,
		"AGE_ROLE_MISMATCH":        8,
		"USERNAME_SENSITIVE_WORDS": 9,
	}
)

func (x UserErrorReason) Enum() *UserErrorReason {
	p := new(UserErrorReason)
	*p = x
	return p
}

func (x UserErrorReason) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UserErrorReason) Descriptor() protoreflect.EnumDescriptor {
	return file_errcodes_user_errors_proto_enumTypes[0].Descriptor()
}

func (UserErrorReason) Type() protoreflect.EnumType {
	return &file_errcodes_user_errors_proto_enumTypes[0]
}

func (x UserErrorReason) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UserErrorReason.Descriptor instead.
func (UserErrorReason) EnumDescriptor() ([]byte, []int) {
	return file_errcodes_user_errors_proto_rawDescGZIP(), []int{0}
}

var File_errcodes_user_errors_proto protoreflect.FileDescriptor

const file_errcodes_user_errors_proto_rawDesc = "" +
	"\n" +
	"\x1aerrcodes/user_errors.proto\x12\berrcodes\x1a\x13errors/errors.proto*\xcf\x02\n" +
	"\x0fUserErrorReason\x12*\n" +
	"\x0eUSER_NOT_FOUND\x10\x00\x1a\x16\xa8E\x94\x03\xb2E\x0f用户不存在\x12)\n" +
	"\rUSER_CONFLICT\x10\x01\x1a\x16\xa8E\x99\x03\xb2E\x0f用户已存在\x123\n" +
	"\x11USERNAME_CONFLICT\x10\x04\x1a\x1c\xa8E\x99\x03\xb2E\x15用户名已被使用\x12-\n" +
	"\x0eEMAIL_CONFLICT\x10\a\x1a\x19\xa8E\x99\x03\xb2E\x12邮箱已被使用\x129\n" +
	"\x11AGE_ROLE_MISMATCH\x10\b\x1a\"\xa8E\x90\x03\xb2E\x1b年龄不符合角色要求\x12@\n" +
	"\x18USERNAME_SENSITIVE_WORDS\x10\t\x1a\"\xa8E\x90\x03\xb2E\x1b用户名包含敏感词汇\x1a\x04\xa0E\x90\x03BCZAgithub.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodesb\x06proto3"

var (
	file_errcodes_user_errors_proto_rawDescOnce sync.Once
	file_errcodes_user_errors_proto_rawDescData []byte
)

func file_errcodes_user_errors_proto_rawDescGZIP() []byte {
	file_errcodes_user_errors_proto_rawDescOnce.Do(func() {
		file_errcodes_user_errors_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_errcodes_user_errors_proto_rawDesc), len(file_errcodes_user_errors_proto_rawDesc)))
	})
	return file_errcodes_user_errors_proto_rawDescData
}

var file_errcodes_user_errors_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_errcodes_user_errors_proto_goTypes = []any{
	(UserErrorReason)(0), // 0: errcodes.UserErrorReason
}
var file_errcodes_user_errors_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_errcodes_user_errors_proto_init() }
func file_errcodes_user_errors_proto_init() {
	if File_errcodes_user_errors_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_errcodes_user_errors_proto_rawDesc), len(file_errcodes_user_errors_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_errcodes_user_errors_proto_goTypes,
		DependencyIndexes: file_errcodes_user_errors_proto_depIdxs,
		EnumInfos:         file_errcodes_user_errors_proto_enumTypes,
	}.Build()
	File_errcodes_user_errors_proto = out.File
	file_errcodes_user_errors_proto_goTypes = nil
	file_errcodes_user_errors_proto_depIdxs = nil
}
