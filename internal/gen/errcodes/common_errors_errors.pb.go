// Code generated by protoc-gen-go-errors. DO NOT EDIT.

package errcodes

import (
	fmt "fmt"
	errors "github.com/go-kratos/kratos/v2/errors"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
const _ = errors.SupportPackageIsVersion1

// 内部错误
func IsInternalError(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_INTERNAL_ERROR.String() && e.Code == 500
}

// 内部错误
func ErrorInternalErrorf(format string, args ...interface{}) *errors.Error {
	return errors.New(500, CommonErrorReason_INTERNAL_ERROR.String(), fmt.Sprintf(format, args...))
}

// 参数错误
func IsBadRequest(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_BAD_REQUEST.String() && e.Code == 400
}

// 参数错误
func ErrorBadRequestf(format string, args ...interface{}) *errors.Error {
	return errors.New(400, CommonErrorReason_BAD_REQUEST.String(), fmt.Sprintf(format, args...))
}

// 认证失败
func IsUnauthorized(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_UNAUTHORIZED.String() && e.Code == 401
}

// 认证失败
func ErrorUnauthorizedf(format string, args ...interface{}) *errors.Error {
	return errors.New(401, CommonErrorReason_UNAUTHORIZED.String(), fmt.Sprintf(format, args...))
}

// 权限不足
func IsForbidden(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_FORBIDDEN.String() && e.Code == 403
}

// 权限不足
func ErrorForbiddenf(format string, args ...interface{}) *errors.Error {
	return errors.New(403, CommonErrorReason_FORBIDDEN.String(), fmt.Sprintf(format, args...))
}

// 资源不存在
func IsNotFound(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_NOT_FOUND.String() && e.Code == 404
}

// 资源不存在
func ErrorNotFoundf(format string, args ...interface{}) *errors.Error {
	return errors.New(404, CommonErrorReason_NOT_FOUND.String(), fmt.Sprintf(format, args...))
}

// 资源冲突
func IsConflict(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_CONFLICT.String() && e.Code == 409
}

// 资源冲突
func ErrorConflictf(format string, args ...interface{}) *errors.Error {
	return errors.New(409, CommonErrorReason_CONFLICT.String(), fmt.Sprintf(format, args...))
}

// 请求太多
func IsTooManyRequest(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_TOO_MANY_REQUEST.String() && e.Code == 429
}

// 请求太多
func ErrorTooManyRequestf(format string, args ...interface{}) *errors.Error {
	return errors.New(429, CommonErrorReason_TOO_MANY_REQUEST.String(), fmt.Sprintf(format, args...))
}

// 请求超时
func IsRequestTimeout(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_REQUEST_TIMEOUT.String() && e.Code == 408
}

// 请求超时
func ErrorRequestTimeoutf(format string, args ...interface{}) *errors.Error {
	return errors.New(408, CommonErrorReason_REQUEST_TIMEOUT.String(), fmt.Sprintf(format, args...))
}

// 服务不可用
func IsServiceUnavailable(err error) bool {
	if err == nil {
		return false
	}
	e := errors.FromError(err)
	return e.Reason == CommonErrorReason_SERVICE_UNAVAILABLE.String() && e.Code == 503
}

// 服务不可用
func ErrorServiceUnavailablef(format string, args ...interface{}) *errors.Error {
	return errors.New(503, CommonErrorReason_SERVICE_UNAVAILABLE.String(), fmt.Sprintf(format, args...))
}

var (
	// 内部错误
	ErrorInternalError *errors.Error
	// 参数错误
	ErrorBadRequest *errors.Error
	// 认证失败
	ErrorUnauthorized *errors.Error
	// 权限不足
	ErrorForbidden *errors.Error
	// 资源不存在
	ErrorNotFound *errors.Error
	// 资源冲突
	ErrorConflict *errors.Error
	// 请求太多
	ErrorTooManyRequest *errors.Error
	// 请求超时
	ErrorRequestTimeout *errors.Error
	// 服务不可用
	ErrorServiceUnavailable *errors.Error
)

func init() {
	ErrorInternalError = ErrorInternalErrorf("内部错误")
	ErrorBadRequest = ErrorBadRequestf("参数错误")
	ErrorUnauthorized = ErrorUnauthorizedf("认证失败")
	ErrorForbidden = ErrorForbiddenf("权限不足")
	ErrorNotFound = ErrorNotFoundf("资源不存在")
	ErrorConflict = ErrorConflictf("资源冲突")
	ErrorTooManyRequest = ErrorTooManyRequestf("请求太多")
	ErrorRequestTimeout = ErrorRequestTimeoutf("请求超时")
	ErrorServiceUnavailable = ErrorServiceUnavailablef("服务不可用")
}
