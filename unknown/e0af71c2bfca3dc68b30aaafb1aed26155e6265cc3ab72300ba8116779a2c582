# 技术栈

## 核心框架
- **Kratos v2**: Go 微服务框架，支持 HTTP/gRPC 服务
- **Go 1.24**: 主要编程语言
- **Protocol Buffers**: API 定义和代码生成

## 核心库和工具

### 代码生成
- **protoc-gen-go**: Protocol buffer Go 代码生成
- **protoc-gen-go-grpc**: gRPC 服务生成
- **protoc-gen-go-http**: Kratos HTTP 传输层生成
- **protoc-gen-openapi**: OpenAPI/Swagger 文档生成
- **protoc-gen-go-kerrors**: 自定义错误码生成
- **Wire**: 依赖注入代码生成
- **Ent**: 数据库 ORM 代码生成

### 校验和中间件
- **buf.validate**: Protocol buffer 校验规则
- **kratos/contrib/middleware/validate**: 校验中间件集成

### 数据库和存储
- **PostgreSQL**: 主数据库（可配置为 MySQL）
- **Redis**: 缓存和会话存储
- **Ent ORM**: 类型安全的数据库操作和迁移

### 日志和可观测性
- **Zerolog**: 结构化日志
- **OpenCensus**: 分布式追踪和指标
- **automaxprocs**: 自动 GOMAXPROCS 调优

## 构建系统和命令

### 环境设置
```bash
# 安装 Kratos CLI 和依赖
make init

# 安装必需工具：
# - protoc-gen-go, protoc-gen-go-grpc
# - protoc-gen-go-http, protoc-gen-openapi
# - wire, protoc-gen-go-kerrors
```

### 代码生成
```bash
# 生成所有代码（推荐用于开发）
make all

# 生成 API 代码（proto -> Go）
make api

# 生成配置 proto
make config

# 生成错误码
make errors

# 生成数据库实体
make ent

# 生成依赖注入
make wire
```

### 构建和运行
```bash
# 构建二进制文件
make build

# 使用配置运行
./bin/server -conf configs/config.yaml

# 运行测试
go test ./...
```

### Docker
```bash
# 构建镜像
docker build -t <镜像名称> .

# 运行容器
docker run --rm -p 8000:8000 -p 9000:9000 \
  -v /path/to/configs:/data/conf <镜像名称>
```

## 开发工作流

1. **定义 API**: 编辑 `api/` 目录中的 `.proto` 文件
2. **生成代码**: 运行 `make all` 重新生成所有代码
3. **实现逻辑**: 在相应层中添加业务逻辑
4. **测试**: 运行 `go test ./...` 进行单元测试
5. **构建**: 使用 `make build` 进行生产构建

## 配置

- **配置文件**: `configs/config.yaml`
- **环境**: 支持 DEV/PROD 模式
- **多租户**: 可配置的租户支持
- **数据库**: PostgreSQL/MySQL 连接池
- **端口**: 默认 HTTP (8000), gRPC (9000)