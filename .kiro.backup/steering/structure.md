# 项目结构

## 架构概览
遵循 **Clean Architecture**，具有清晰的分层和依赖倒置：

```
Proto 层 → Service 层 → Biz 层 → Domain 层 → Data 层
```

## 目录结构

### API 定义
- `api/` - 公共 API proto 定义（开发者维护）
- `protos/` - 内部 proto 定义（错误码、共享类型）
- `third_party/` - 外部 proto 依赖（Google API、验证）

### 应用程序入口
- `cmd/server/` - 主应用程序入口点
  - `main.go` - 应用程序引导
  - `wire.go` - 依赖注入配置
  - `wire_gen.go` - 生成的 DI 代码（自动生成）

### 内部分层（Clean Architecture）
- `internal/service/` - **Service 层**: gRPC/HTTP 处理器，DTO 转换
- `internal/biz/` - **Business 层**: 用例，业务流程验证
- `internal/domain/` - **Domain 层**: 业务实体，领域规则
- `internal/data/` - **Data 层**: 仓储实现，数据库访问
  - `schema/` - Ent 数据库模式定义（开发者维护）

### 生成代码
- `internal/gen/` - **自动生成代码**（请勿编辑）
  - `api/` - 从 proto 文件生成的 API 客户端
  - `database/ent/` - 生成的 Ent ORM 代码
  - `errcodes/` - 生成的错误码定义

### 配置和部署
- `configs/` - 配置文件（YAML）
- `deploy/` - 部署配置（Kubernetes、Docker）
- `bin/` - 编译后的二进制文件（构建时创建）

### 工具类
- `internal/utils/` - 共享工具（日志、追踪、元数据）
- `internal/server/` - 服务器设置（HTTP、gRPC、依赖注入）
- `internal/conf/` - 配置 proto 定义

## 分层职责

### Service 层 (`internal/service/`)
- 处理 gRPC/HTTP 请求
- 在 proto DTO 和领域模型之间转换
- 提取上下文信息（用户 ID、租户 ID）
- 基本的请求/响应转换

### Biz 层 (`internal/biz/`)
- 实现用例和业务工作流
- 业务级验证（唯一性、权限）
- 编排领域对象和仓储
- 处理横切关注点（日志、事务）

### Domain 层 (`internal/domain/`)
- 定义业务实体和值对象
- 实现特定于领域的业务规则
- 定义仓储接口（依赖倒置）
- 纯业务逻辑，无外部依赖

### Data 层 (`internal/data/`)
- 实现仓储接口
- 通过 Ent ORM 处理数据库操作
- 管理数据持久化和检索
- `schema/` 中的数据库模式定义

## 文件命名约定
- `*_test.go` - 与实现文件并列的单元测试
- `*.proto` - Protocol buffer 定义
- `*.pb.go` - 生成的 protobuf 代码
- `*_grpc.pb.go` - 生成的 gRPC 服务代码
- `*_http.pb.go` - 生成的 HTTP 传输代码
- `wire.go` - 依赖注入配置
- `wire_gen.go` - 生成的 DI 代码

## 代码生成流程
1. **Proto 文件** (`api/`, `protos/`) → 生成 API 客户端
2. **Ent 模式** (`internal/data/schema/`) → 生成 ORM 代码
3. **Wire 配置** (`cmd/server/wire.go`) → 生成 DI 代码
4. 所有生成的代码都放在 `internal/gen/`（受 Go internal 包保护）