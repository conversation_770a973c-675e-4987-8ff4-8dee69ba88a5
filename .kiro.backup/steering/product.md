# 产品概述

这是一个基于 [Kratos](https://go-kratos.dev/) 框架构建的 **Kratos 分层校验架构演示项目**。

## 核心目标

展示 Go 微服务中清晰架构的实现，包含：
- **分层职责明确**: Proto、Service、Biz、Domain、Data 各层职责清晰分离
- **校验策略优化**: 避免重复校验，提升性能和可维护性
- **完整业务示例**: 包含用户管理的完整业务流程演示
- **最佳实践展示**: DDD、Clean Architecture 等设计模式的实际应用
- **测试覆盖完整**: 各层都有对应的单元测试

## 主要特性

- **多租户架构**: 所有实体都包含租户隔离
- **基于角色的访问控制**: 用户、管理员、超级管理员角色及权限检查
- **中文语言支持**: 支持中文姓名和内容的验证模式
- **错误处理**: 自定义错误码，支持国际化
- **Protocol Buffers**: 使用 buf.validate 的 gRPC 和 HTTP API
- **数据库 ORM**: 使用 Ent 框架进行类型安全的数据库操作

## 业务领域

用户管理系统，支持 CRUD 操作，包括：
- 用户创建及验证（姓名、年龄、邮箱、角色）
- 多租户数据隔离
- 基于角色的权限和业务规则
- 邮箱唯一性约束
- 年龄-角色验证（如：管理员必须年满18岁）