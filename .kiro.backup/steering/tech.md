# 技术栈

## 核心框架
- **Kratos v2**: Go 微服务框架，支持 gRPC/HTTP API
- **Go 1.24+**: 主要编程语言
- **Protocol Buffers**: API 定义和代码生成

## 主要库和工具
- **Ent**: 类型安全的数据库 ORM
- **Wire**: 依赖注入代码生成
- **buf.validate**: Protocol buffer 验证
- **Zerolog**: 结构化日志
- **PostgreSQL**: 主数据库（可配置为 MySQL）
- **Redis**: 缓存层

## 代码生成工具
- `protoc-gen-go`: Go protobuf 代码生成
- `protoc-gen-go-grpc`: gRPC 服务生成
- `protoc-gen-go-http`: Kratos HTTP 传输生成
- `protoc-gen-openapi`: OpenAPI 规范生成
- `protoc-gen-go-kerrors`: 自定义错误码生成

## 构建系统
使用 **Makefile** 进行所有构建操作：

### 常用命令
```bash
# 初始化开发环境
make init

# 生成所有代码（推荐用于大多数更改）
make all

# 构建应用程序
make build

# 生成特定组件
make api      # 从 proto 文件生成 API 代码
make config   # 生成内部 proto 配置
make errors   # 生成错误码
make ent      # 生成 Ent 数据库代码
make wire     # 生成依赖注入

# 运行应用程序
./bin/server -conf configs/config.yaml
```

### 开发工作流
1. 修改 `api/` 或 `protos/` 中的 proto 文件
2. 更新 `internal/data/schema/` 中的 Ent 模式
3. 运行 `make all` 重新生成代码
4. 运行 `make build` 编译
5. 使用 `go test ./...` 测试

## 配置
- **基于 YAML**: `configs/config.yaml`
- **多环境**: DEV/PROD 模式
- **多租户**: 可配置的租户隔离
- **数据库**: 支持 PostgreSQL/MySQL
- **端口**: HTTP :8000, gRPC :9000

## 验证策略
- **Proto 层**: 格式、类型、基础范围验证（buf.validate）
- **Service 层**: DTO 转换、上下文提取
- **Biz 层**: 业务流程、权限、存在性检查
- **Domain 层**: 业务规则、跨字段验证