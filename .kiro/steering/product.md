# 产品概述

这是一个 **Kratos 分层校验架构演示项目** - 基于 [Kratos](https://go-kratos.dev/) 框架构建的微服务，展示了清晰的架构原则和高效的参数校验策略。

## 核心特性

- **分层职责明确**: Proto、Service、Biz、Domain、Data 各层职责清晰分离
- **校验策略优化**: 避免重复校验，提升性能和可维护性
- **完整业务示例**: 包含用户管理的完整业务流程演示
- **最佳实践展示**: DDD、Clean Architecture 等设计模式的实际应用
- **测试覆盖完整**: 各层都有对应的单元测试

## 核心业务领域

项目以 **用户管理** 为主要业务领域，实现了：
- 用户 CRUD 操作（创建、读取、更新、删除）
- 基于角色的访问控制（普通用户、管理员、超级管理员）
- 多租户支持
- 邮箱唯一性校验
- 年龄-角色业务规则校验

## 架构理念

项目遵循 **分层校验方法**，每层都有特定的校验职责：
- **Proto 层**: 使用 buf.validate 进行格式、类型和基础范围校验
- **Service 层**: DTO 转换和上下文处理
- **Biz 层**: 业务流程校验、权限检查和存在性检查
- **Domain 层**: 业务规则校验和领域逻辑
- **Data 层**: 数据持久化和存在性验证