# 项目结构和组织

## 目录布局

```
├── api/                    # API 定义（proto 文件）
│   └── user/v1/           # 用户服务 API v1
├── cmd/                   # 应用程序入口点
│   └── server/            # 主服务器应用程序
├── configs/               # 配置文件
├── internal/              # 私有应用程序代码
│   ├── biz/              # 业务逻辑层
│   ├── conf/             # 配置 proto 定义
│   ├── data/             # 数据访问层
│   ├── domain/           # 领域模型和接口
│   ├── gen/              # 生成的代码（请勿编辑）
│   ├── server/           # 服务器设置和中间件
│   ├── service/          # 服务层（gRPC/HTTP 处理器）
│   └── utils/            # 工具函数
├── protos/               # 错误码定义
│   └── errcodes/         # 业务错误定义
├── third_party/          # 第三方 proto 依赖
└── deploy/               # 部署配置
```

## 分层架构和职责

### 1. API 层 (`api/`)
- **目的**: 外部 API 的 Protocol buffer 定义
- **包含**: 服务定义、请求/响应消息
- **校验**: 使用 `buf.validate` 进行基本格式校验
- **命名**: 使用版本化目录（v1、v2 等）

### 2. Service 层 (`internal/service/`)
- **目的**: gRPC/HTTP 请求处理器和 DTO 转换
- **职责**:
  - 将 proto 消息转换为领域对象
  - 提取上下文信息（user_id、tenant_id）
  - 调用业务逻辑层
  - 将领域对象转换回 proto 响应
- **校验**: 上下文校验、身份验证检查
- **模式**: 每个领域一个服务文件（如 `user.go`）

### 3. 业务逻辑层 (`internal/biz/`)
- **目的**: 业务用例和工作流
- **职责**:
  - 编排业务流程
  - 业务规则校验
  - 权限和授权检查
  - 跨实体业务逻辑
- **模式**: 使用接口的 UseCase 模式
- **命名**: 实现领域接口的 `*Usecase` 结构体

### 4. Domain 层 (`internal/domain/`)
- **目的**: 核心业务实体和规则
- **包含**:
  - 领域模型（结构体）
  - 业务接口（Repository、UseCase）
  - 领域特定的校验方法
  - 业务规则执行
- **模式**: 具有行为的富领域模型
- **独立性**: 无外部依赖

### 5. Data 层 (`internal/data/`)
- **目的**: 数据持久化和外部服务集成
- **职责**:
  - 数据库操作
  - 外部 API 调用
  - 数据存在性校验
  - Repository 模式实现
- **ORM**: 使用 Ent 进行类型安全的数据库操作
- **Schema**: 在 `schema/` 子目录中定义数据库模式

### 6. 生成代码 (`internal/gen/`)
- **目的**: 从 proto 定义自动生成的代码
- **结构**:
  - `api/`: 生成的 API 代码（pb.go、grpc.pb.go、http.pb.go）
  - `database/ent/`: 生成的 Ent ORM 代码
  - `errcodes/`: 生成的错误码定义
- **规则**: 绝不手动编辑此目录中的文件

## 文件命名约定

### Go 文件
- **服务**: `{domain}.go`（如 `user.go`）
- **测试**: `{domain}_test.go`（如 `user_test.go`）
- **仓储**: `{domain}_repo.go`（如 `user_repo.go`）
- **Wire**: `wire.go` 用于依赖注入设置

### Proto 文件
- **API**: `{domain}.proto`（如 `user.proto`）
- **错误**: `{domain}_errors.proto`（如 `user_errors.proto`）
- **配置**: `conf.proto` 用于配置定义

## 导入约定

### 标准导入
```go
// 标准库优先
import (
    "context"
    "fmt"
)

// 第三方包
import (
    "github.com/go-kratos/kratos/v2/log"
    "google.golang.org/protobuf/types/known/emptypb"
)

// 本地包（项目特定）
import (
    "github.com/go-kratos/kratos-layout/internal/domain"
    userv1 "github.com/go-kratos/kratos-layout/internal/gen/api/user/v1"
    ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"
)
```

### 错误码导入
- **始终使用别名**: `ec "github.com/go-kratos/kratos-layout/internal/gen/errcodes"`
- **用法**: `ec.ErrorUserNotFound`、`ec.ErrorEmailConflict`

## 代码组织模式

### Repository 模式
- 接口在 `domain/` 包中
- 实现在 `data/` 包中
- 通过 Wire 进行依赖注入

### UseCase 模式
- 接口在 `domain/` 包中
- 实现在 `biz/` 包中
- 编排多个仓储

### 错误处理
- 在 `protos/errcodes/*.proto` 中定义错误
- 使用 `make errors` 生成
- 在所有层中一致使用
- 层间不包装错误 - 直接传递

### 测试结构
- 单元测试与源文件并列
- 集成测试在单独的 `_test` 包中
- 模拟接口用于测试隔离

## 配置管理

### 环境特定配置
- `configs/config.yaml`: 主配置
- 敏感数据使用环境变量
- 多租户配置支持

### 功能标志
- 数据库迁移模式
- 租户启用
- 调试/生产模式