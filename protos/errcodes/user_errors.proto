syntax = "proto3";

package errcodes;

import "errors/errors.proto";

option go_package = "github.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodes";

// UserErrorReason 定义用户服务相关错误码
enum UserErrorReason {
  // 设置错误码前缀
  option (errors.default_code) = 400;

  // 用户未找到
  USER_NOT_FOUND = 0 [(errors.code) = 404, (errors.message) = "用户不存在"];
  
  // 用户冲突
  USER_CONFLICT = 1 [(errors.code) = 409, (errors.message) = "用户已存在"];
  
  // 用户名冲突
  USERNAME_CONFLICT = 4 [(errors.code) = 409, (errors.message) = "用户名已被使用"];

  // 邮箱冲突
  EMAIL_CONFLICT = 7 [(errors.code) = 409, (errors.message) = "邮箱已被使用"];

  // 年龄不符合角色要求
  AGE_ROLE_MISMATCH = 8 [(errors.code) = 400, (errors.message) = "年龄不符合角色要求"];

  // 用户名包含敏感词汇
  USERNAME_SENSITIVE_WORDS = 9 [(errors.code) = 400, (errors.message) = "用户名包含敏感词汇"];
}
