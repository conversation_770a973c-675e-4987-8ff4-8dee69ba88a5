syntax = "proto3";

package errcodes;

import "errors/errors.proto";

option go_package = "github.com/go-kratos/kratos-layout/internal/gen/errcodes;errcodes";

// CommonErrorReason 定义通用错误码
enum CommonErrorReason {
  option (errors.default_code) = 500;

  // 内部错误
  INTERNAL_ERROR = 0 [(errors.code) = 500];
  // 参数错误
  BAD_REQUEST = 1 [(errors.code) = 400];
  // 认证失败
  UNAUTHORIZED = 2 [(errors.code) = 401];
  // 权限不足
  FORBIDDEN = 3 [(errors.code) = 403];
  // 资源不存在
  NOT_FOUND = 4 [(errors.code) = 404];
  // 资源冲突
  CONFLICT = 5 [(errors.code) = 409];
  // 请求太多
  TOO_MANY_REQUEST = 6 [(errors.code) = 429];
  // 请求超时
  REQUEST_TIMEOUT = 7 [(errors.code) = 408];
  // 服务不可用
  SERVICE_UNAVAILABLE = 8 [(errors.code) = 503];
}
