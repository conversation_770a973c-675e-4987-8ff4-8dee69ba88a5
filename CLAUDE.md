# Kratos分层校验架构脚手架

## Steering Documents

项目包含以下指导文档，为AI助手提供项目特定的指导和约定：

### [产品指导文档](.claude/steering/product.md)
- **路径**: `.claude/steering/product.md`
- **描述**: 定义项目定位、核心特性和业务逻辑规则。明确这是一个Kratos微服务脚手架和参考模板，包含用户管理业务规则和校验职责矩阵。

### [技术指导文档](.claude/steering/tech.md)
- **路径**: `.claude/steering/tech.md`
- **描述**: 技术栈、构建系统、常用命令和项目约定。包含脚手架维护原则、代码生成工作流、错误处理和校验策略。

### [项目结构指导文档](.claude/steering/structure.md)
- **路径**: `.claude/steering/structure.md`
- **描述**: 目录组织、文件命名模式、组件架构和关键文件位置。提供分层架构模式和模板维护指导。